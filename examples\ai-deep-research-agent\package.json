{"name": "my-motia-project", "description": "", "scripts": {"postinstall": "motia install", "dev": "motia dev", "dev:debug": "motia dev --verbose", "generate-types": "motia generate-types", "build": "motia build", "clean": "rm -rf dist node_modules python_modules .motia .mermaid"}, "keywords": ["motia"], "dependencies": {"@mendable/firecrawl-js": "^1.21.0", "dotenv": "^16.5.0", "firecrawl": "^1.21.0", "motia": "^0.5.5-beta.113", "node-fetch": "^3.3.2", "openai": "^5.7.0", "react": "^19.0.0", "zod": "^3.25.67"}, "devDependencies": {"@types/node-fetch": "^2.6.12", "@types/react": "^18.3.23", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}